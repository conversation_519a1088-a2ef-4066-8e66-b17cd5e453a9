<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>古诗学习乐园 - 四合一游戏平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        parchment: '#f9f5f0',
                        brown: '#6d5b4b',
                        gold: '#d4af37',
                        green: '#2e7d32',
                        red: '#c62828',
                        paper: '#fdfaf3',
                        primary: '#6a5a4a',
                        highlight: '#5a8b82',
                        poemBg: '#f8f4ea',
                        background: '#f5f1e8',
                        secondary: '#8b7355',
                        accent: '#d4af37'
                    },
                    fontFamily: {
                        song: ['"Noto Serif SC"', 'serif'],
                        kai: ['KaiTi', 'STKaiti', 'Sim<PERSON><PERSON>', 'serif']
                    }
                }
            }
        }
    </script>

    <style>
        /* 自定义样式 */
        .tab-active {
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
        }

        .poem-selector {
            transition: all 0.3s ease;
        }

        .poem-selector.collapsed {
            max-height: 60px;
            overflow: hidden;
        }

        .poem-selector.expanded {
            max-height: 500px;
        }

        .game-content {
            min-height: 600px;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .poem-card {
            transition: all 0.2s ease;
        }

        .poem-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .poem-card.selected {
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            color: white;
        }

        /* 古诗默写助手样式 */
        .blank-char {
            display: inline-block;
            width: 1.5rem;
            height: 2rem;
            border-bottom: 2px solid #6b7280;
            margin: 0 2px;
            vertical-align: bottom;
        }

        .blank-section {
            display: inline;
        }

        .answer-reveal {
            color: #059669;
            font-weight: bold;
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            padding: 2px 4px;
            border-radius: 4px;
            animation: revealChar 0.5s ease-out forwards;
        }

        .reveal-animation {
            opacity: 0;
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes revealChar {
            0% { transform: translateY(100%); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes fadeInUp {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        /* 古诗小侦探样式 */
        .error-found {
            text-decoration: underline wavy #2e7d32;
            background-color: #c8e6c9;
            color: #1b5e20;
        }

        .error-highlight {
            text-decoration: underline wavy #c62828;
            background-color: #ffcdd2;
        }

        .highlight-find {
            animation: highlight 1.5s ease-in-out;
        }

        @keyframes highlight {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
    </style>
</head>
<body class="bg-parchment min-h-screen font-song text-brown">
    <!-- 页眉 -->
    <header class="bg-gradient-to-r from-gold to-yellow-400 text-white p-6 shadow-lg">
        <div class="container mx-auto text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-2">
                <i class="fa fa-book mr-3"></i>古诗学习乐园
            </h1>
            <p class="text-xl opacity-90">四合一游戏平台，让学习古诗更有趣！</p>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-6">
        <!-- 游戏导航标签 -->
        <div class="bg-white rounded-xl shadow-lg p-4 mb-6">
            <div class="flex flex-wrap justify-center gap-2 md:gap-4">
                <button id="tab-detective" class="tab-btn tab-active px-6 py-3 rounded-lg font-bold transition-all duration-300">
                    <i class="fa fa-search mr-2"></i>古诗小侦探
                </button>
                <button id="tab-recite" class="tab-btn px-6 py-3 rounded-lg font-bold transition-all duration-300 bg-gray-100 hover:bg-gray-200">
                    <i class="fa fa-pencil mr-2"></i>古诗默写助手
                </button>
                <button id="tab-shadow" class="tab-btn px-6 py-3 rounded-lg font-bold transition-all duration-300 bg-gray-100 hover:bg-gray-200">
                    <i class="fa fa-eye mr-2"></i>诗影寻踪
                </button>
                <button id="tab-connect" class="tab-btn px-6 py-3 rounded-lg font-bold transition-all duration-300 bg-gray-100 hover:bg-gray-200">
                    <i class="fa fa-link mr-2"></i>连句成诗
                </button>
            </div>
        </div>

        <!-- 古诗选择面板 -->
        <div id="poem-selector" class="poem-selector collapsed bg-white rounded-xl shadow-lg mb-6">
            <div class="p-4 border-b cursor-pointer" onclick="togglePoemSelector()">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold text-primary">
                        <i class="fa fa-list mr-2"></i>古诗选择
                        <span id="selected-count" class="text-sm text-gray-500 ml-2">(已选择: 0首)</span>
                    </h3>
                    <i id="selector-arrow" class="fa fa-chevron-down transition-transform duration-300"></i>
                </div>
            </div>

            <div id="selector-content" class="p-4">
                <!-- 筛选区域 -->
                <div class="flex flex-wrap gap-4 mb-4">
                    <select id="grade-filter" class="px-3 py-2 border rounded-lg">
                        <option value="">所有年级</option>
                        <option value="1">一年级</option>
                        <option value="2">二年级</option>
                        <option value="3">三年级</option>
                        <option value="4">四年级</option>
                        <option value="5">五年级</option>
                        <option value="6">六年级</option>
                    </select>

                    <select id="author-filter" class="px-3 py-2 border rounded-lg">
                        <option value="">所有作者</option>
                    </select>

                    <button id="select-all-btn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        全选
                    </button>

                    <button id="random-select-btn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                        随机选择5首
                    </button>
                </div>

                <!-- 古诗列表 -->
                <div id="poems-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
                    <!-- 古诗卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 确认按钮 -->
                <div class="mt-4 text-center">
                    <button id="confirm-selection-btn" class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600 transition-colors">
                        确认选择并开始游戏
                    </button>
                </div>
            </div>
        </div>

        <!-- 游戏内容区域 -->
        <div id="game-area" class="game-content">
            <!-- 古诗小侦探 -->
            <div id="game-detective" class="game-panel fade-in">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-2">
                            <i class="fa fa-search mr-2"></i>古诗小侦探
                        </h2>
                        <p class="text-gray-600">找出古诗中的错别字，成为古诗小侦探！</p>
                    </div>

                    <div id="detective-content" class="text-center">
                        <div class="bg-poemBg rounded-lg p-8 mb-6">
                            <p class="text-lg text-gray-600 mb-4">请先选择古诗，然后开始游戏</p>
                            <button class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600" onclick="togglePoemSelector()">
                                选择古诗
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 古诗默写助手 -->
            <div id="game-recite" class="game-panel hidden">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-2">
                            <i class="fa fa-pencil mr-2"></i>古诗默写助手
                        </h2>
                        <p class="text-gray-600">线上出题，线下书写，即时核对</p>
                    </div>

                    <div id="recite-content" class="text-center">
                        <div class="bg-poemBg rounded-lg p-8 mb-6">
                            <p class="text-lg text-gray-600 mb-4">请先选择古诗，然后开始默写练习</p>
                            <button class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600" onclick="togglePoemSelector()">
                                选择古诗
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 诗影寻踪 -->
            <div id="game-shadow" class="game-panel hidden">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-2">
                            <i class="fa fa-eye mr-2"></i>诗影寻踪
                        </h2>
                        <p class="text-gray-600">根据提示猜古诗，锻炼理解能力</p>
                    </div>

                    <div id="shadow-content" class="text-center">
                        <div class="bg-poemBg rounded-lg p-8 mb-6">
                            <p class="text-lg text-gray-600 mb-4">请先选择古诗，然后开始猜诗游戏</p>
                            <button class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600" onclick="togglePoemSelector()">
                                选择古诗
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 连句成诗 -->
            <div id="game-connect" class="game-panel hidden">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-2">
                            <i class="fa fa-link mr-2"></i>连句成诗
                        </h2>
                        <p class="text-gray-600">将打乱的诗句重新排序，完成古诗</p>
                    </div>

                    <div id="connect-content" class="text-center">
                        <div class="bg-poemBg rounded-lg p-8 mb-6">
                            <p class="text-lg text-gray-600 mb-4">请先选择古诗，然后开始排序游戏</p>
                            <button class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600" onclick="togglePoemSelector()">
                                选择古诗
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white p-6 mt-8">
        <div class="container mx-auto text-center">
            <p class="text-lg mb-2">古诗学习乐园 &copy; 2025 | 让学习古诗更有趣</p>
            <div class="text-gray-400 text-sm">
                <span class="mx-2">四合一游戏平台</span>
                <span class="mx-2">|</span>
                <span class="mx-2">寓教于乐</span>
                <span class="mx-2">|</span>
                <span class="mx-2">传承经典</span>
            </div>
        </div>
    </footer>

    <!-- 引入古诗数据 -->
    <script src="js/poems-data.js"></script>

    <!-- 主要JavaScript逻辑 -->
    <script>
        // 全局变量
        let selectedPoems = [];
        let currentGame = 'detective';
        let allPoems = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');
            initializePage();
        });

        // 初始化页面
        function initializePage() {
            // 检查古诗数据是否加载
            if (typeof poemDatabase === 'undefined') {
                console.error('古诗数据未加载！');
                return;
            }

            allPoems = poemDatabase;
            console.log('加载了', allPoems.length, '首古诗');

            // 初始化古诗选择面板
            initializePoemSelector();

            // 初始化标签页切换
            initializeTabs();

            // 默认选择一些古诗
            selectRandomPoems(5);
        }

        // 初始化古诗选择面板
        function initializePoemSelector() {
            // 填充作者下拉框
            const authorFilter = document.getElementById('author-filter');
            const authors = [...new Set(allPoems.map(poem => poem.author))].sort();
            authors.forEach(author => {
                const option = document.createElement('option');
                option.value = author;
                option.textContent = author;
                authorFilter.appendChild(option);
            });

            // 渲染古诗网格
            renderPoemsGrid();

            // 绑定事件
            document.getElementById('grade-filter').addEventListener('change', filterPoems);
            document.getElementById('author-filter').addEventListener('change', filterPoems);
            document.getElementById('select-all-btn').addEventListener('click', selectAllPoems);
            document.getElementById('random-select-btn').addEventListener('click', () => selectRandomPoems(5));
            document.getElementById('confirm-selection-btn').addEventListener('click', confirmSelection);
        }

        // 渲染古诗网格
        function renderPoemsGrid(poemsToShow = allPoems) {
            const grid = document.getElementById('poems-grid');
            grid.innerHTML = '';

            poemsToShow.forEach((poem, index) => {
                const card = document.createElement('div');
                card.className = 'poem-card p-3 border rounded-lg cursor-pointer';
                card.innerHTML = `
                    <div class="font-bold text-sm mb-1">${poem.title}</div>
                    <div class="text-xs text-gray-600">${poem.author} · ${poem.grade}年级</div>
                `;

                // 检查是否已选择
                if (selectedPoems.some(p => p.title === poem.title)) {
                    card.classList.add('selected');
                }

                card.addEventListener('click', () => togglePoemSelection(poem, card));
                grid.appendChild(card);
            });
        }

        // 切换古诗选择状态
        function togglePoemSelection(poem, cardElement) {
            const index = selectedPoems.findIndex(p => p.title === poem.title);

            if (index > -1) {
                // 取消选择
                selectedPoems.splice(index, 1);
                cardElement.classList.remove('selected');
            } else {
                // 添加选择
                selectedPoems.push(poem);
                cardElement.classList.add('selected');
            }

            updateSelectedCount();
        }

        // 更新已选择数量显示
        function updateSelectedCount() {
            document.getElementById('selected-count').textContent = `(已选择: ${selectedPoems.length}首)`;
        }

        // 筛选古诗
        function filterPoems() {
            const gradeFilter = document.getElementById('grade-filter').value;
            const authorFilter = document.getElementById('author-filter').value;

            let filteredPoems = allPoems;

            if (gradeFilter) {
                filteredPoems = filteredPoems.filter(poem => poem.grade === gradeFilter);
            }

            if (authorFilter) {
                filteredPoems = filteredPoems.filter(poem => poem.author === authorFilter);
            }

            renderPoemsGrid(filteredPoems);
        }

        // 全选古诗
        function selectAllPoems() {
            const gradeFilter = document.getElementById('grade-filter').value;
            const authorFilter = document.getElementById('author-filter').value;

            let poemsToSelect = allPoems;

            if (gradeFilter) {
                poemsToSelect = poemsToSelect.filter(poem => poem.grade === gradeFilter);
            }

            if (authorFilter) {
                poemsToSelect = poemsToSelect.filter(poem => poem.author === authorFilter);
            }

            selectedPoems = [...poemsToSelect];
            updateSelectedCount();
            renderPoemsGrid(poemsToSelect);
        }

        // 随机选择古诗
        function selectRandomPoems(count) {
            const shuffled = [...allPoems].sort(() => 0.5 - Math.random());
            selectedPoems = shuffled.slice(0, count);
            updateSelectedCount();
            renderPoemsGrid();
        }

        // 确认选择
        function confirmSelection() {
            if (selectedPoems.length === 0) {
                alert('请至少选择一首古诗！');
                return;
            }

            // 折叠选择面板
            togglePoemSelector();

            // 初始化当前游戏
            initializeCurrentGame();

            alert(`已选择 ${selectedPoems.length} 首古诗，开始游戏！`);
        }

        // 切换古诗选择面板
        function togglePoemSelector() {
            const selector = document.getElementById('poem-selector');
            const arrow = document.getElementById('selector-arrow');

            if (selector.classList.contains('collapsed')) {
                selector.classList.remove('collapsed');
                selector.classList.add('expanded');
                arrow.style.transform = 'rotate(180deg)';
            } else {
                selector.classList.remove('expanded');
                selector.classList.add('collapsed');
                arrow.style.transform = 'rotate(0deg)';
            }
        }

        // 初始化标签页切换
        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-btn');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const gameId = button.id.replace('tab-', '');
                    switchToGame(gameId);
                });
            });
        }

        // 切换游戏
        function switchToGame(gameId) {
            // 更新当前游戏
            currentGame = gameId;

            // 更新标签按钮样式
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('tab-active');
                btn.classList.add('bg-gray-100', 'hover:bg-gray-200');
            });

            const activeTab = document.getElementById(`tab-${gameId}`);
            activeTab.classList.add('tab-active');
            activeTab.classList.remove('bg-gray-100', 'hover:bg-gray-200');

            // 隐藏所有游戏面板
            document.querySelectorAll('.game-panel').forEach(panel => {
                panel.classList.add('hidden');
            });

            // 显示当前游戏面板
            const currentPanel = document.getElementById(`game-${gameId}`);
            currentPanel.classList.remove('hidden');
            currentPanel.classList.add('fade-in');

            // 初始化当前游戏
            initializeCurrentGame();
        }

        // 初始化当前游戏
        function initializeCurrentGame() {
            if (selectedPoems.length === 0) {
                return;
            }

            console.log(`初始化游戏: ${currentGame}, 古诗数量: ${selectedPoems.length}`);

            // 根据不同游戏调用不同的初始化函数
            switch (currentGame) {
                case 'detective':
                    initializeDetectiveGame();
                    break;
                case 'recite':
                    initializeReciteGame();
                    break;
                case 'shadow':
                    initializeShadowGame();
                    break;
                case 'connect':
                    initializeConnectGame();
                    break;
            }
        }

        // 初始化古诗小侦探游戏
        function initializeDetectiveGame() {
            const content = document.getElementById('detective-content');
            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-8">
                    <h3 class="text-xl font-bold mb-4">古诗小侦探已准备就绪！</h3>
                    <p class="text-gray-600 mb-4">已加载 ${selectedPoems.length} 首古诗</p>
                    <button class="px-6 py-3 bg-red text-white rounded-lg font-bold hover:bg-red-600" onclick="startDetectiveGame()">
                        开始找错字
                    </button>
                </div>
            `;
        }

        // 初始化古诗默写助手游戏
        function initializeReciteGame() {
            const content = document.getElementById('recite-content');
            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-8">
                    <h3 class="text-xl font-bold mb-4">古诗默写助手已准备就绪！</h3>
                    <p class="text-gray-600 mb-4">已加载 ${selectedPoems.length} 首古诗</p>
                    <button class="px-6 py-3 bg-highlight text-white rounded-lg font-bold hover:bg-blue-600" onclick="startReciteGame()">
                        开始默写练习
                    </button>
                </div>
            `;
        }

        // 初始化诗影寻踪游戏
        function initializeShadowGame() {
            const content = document.getElementById('shadow-content');
            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-8">
                    <h3 class="text-xl font-bold mb-4">诗影寻踪已准备就绪！</h3>
                    <p class="text-gray-600 mb-4">已加载 ${selectedPoems.length} 首古诗</p>
                    <button class="px-6 py-3 bg-green text-white rounded-lg font-bold hover:bg-green-600" onclick="startShadowGame()">
                        开始猜诗游戏
                    </button>
                </div>
            `;
        }

        // 初始化连句成诗游戏
        function initializeConnectGame() {
            const content = document.getElementById('connect-content');
            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-8">
                    <h3 class="text-xl font-bold mb-4">连句成诗已准备就绪！</h3>
                    <p class="text-gray-600 mb-4">已加载 ${selectedPoems.length} 首古诗</p>
                    <button class="px-6 py-3 bg-accent text-white rounded-lg font-bold hover:bg-yellow-600" onclick="startConnectGame()">
                        开始排序游戏
                    </button>
                </div>
            `;
        }

        // ========== 古诗小侦探游戏逻辑 ==========
        let detectiveGame = {
            currentPoem: null,
            errors: [],
            foundErrors: [],
            currentSelectedError: null
        };

        // 为每首诗定义可能的错误类型
        const poemErrors = {
            "村居": {
                homophones: { "莺": "鹰", "醉": "最", "鸢": "鸳" },
                similar: { "堤": "提", "杨": "扬", "鸢": "鸳" },
                reverse: ["东风", "纸鸢"]
            },
            "咏柳": {
                homophones: { "妆": "装", "绦": "涛", "裁": "才" },
                similar: { "碧": "壁", "绦": "条", "剪": "箭" },
                reverse: ["春风", "剪刀"]
            },
            "赋得古原草送别": {
                homophones: { "离": "梨", "岁": "穗", "尽": "进" },
                similar: { "原": "园", "荣": "菜", "烧": "浇" },
                reverse: ["春风", "野火"]
            },
            "晓出净慈寺送林子方": {
                homophones: { "毕": "必", "竟": "镜", "莲": "连" },
                similar: { "竟": "竞", "碧": "壁", "映": "应" },
                reverse: ["西湖", "荷花"]
            },
            "绝句": {
                homophones: { "鹂": "梨", "鹭": "路", "泊": "博" },
                similar: { "鹭": "鹜", "岭": "领", "窗": "囱" },
                reverse: ["黄鹂", "东吴"]
            },
            "悯农": {
                homophones: { "粟": "素", "颗": "棵", "闲": "贤" },
                similar: { "粟": "栗", "颗": "棵", "犹": "优" },
                reverse: ["秋收", "四海"]
            },
            "舟夜书所见": {
                homophones: { "渔": "鱼", "簇": "促", "作": "坐" },
                similar: { "渔": "鱼", "萤": "荧", "簇": "族" },
                reverse: ["渔灯", "满河"]
            }
        };

        function startDetectiveGame() {
            if (selectedPoems.length === 0) {
                alert('请先选择古诗！');
                return;
            }

            // 随机选择一首诗
            detectiveGame.currentPoem = selectedPoems[Math.floor(Math.random() * selectedPoems.length)];
            detectiveGame.errors = [];
            detectiveGame.foundErrors = [];
            detectiveGame.currentSelectedError = null;

            console.log('开始古诗小侦探游戏，选择的诗：', detectiveGame.currentPoem.title);

            // 生成错误
            generateDetectiveErrors();

            // 渲染游戏界面
            renderDetectiveGame();
        }

        // 生成错误（直接使用原版逻辑）
        function generateDetectiveErrors() {
            const poem = detectiveGame.currentPoem;
            const poemErrorData = poemErrors[poem.title];

            if (!poemErrorData) {
                console.error('找不到诗歌的错误数据：', poem.title);
                return;
            }

            // 决定错误数量 (2-3个)
            const errorCount = Math.floor(Math.random() * 2) + 2;

            // 可用的错误类型
            const errorTypes = ['homophones', 'similar', 'reverse'];

            // 已经使用的位置
            const usedPositions = new Set();

            // 生成错误
            for (let i = 0; i < errorCount; i++) {
                // 随机选择错误类型
                const errorType = errorTypes[Math.floor(Math.random() * errorTypes.length)];

                // 根据错误类型生成错误
                if (errorType === 'reverse') {
                    // 词序颠倒错误
                    const reverseWords = poemErrorData.reverse;
                    if (reverseWords.length > 0) {
                        const randomIndex = Math.floor(Math.random() * reverseWords.length);
                        const word = reverseWords[randomIndex];
                        const position = poem.content.indexOf(word);

                        if (position !== -1 && !usedPositions.has(position)) {
                            detectiveGame.errors.push({
                                type: 'reverse',
                                original: word,
                                error: word.split('').reverse().join(''),
                                position: position
                            });
                            usedPositions.add(position);
                        }
                    }
                } else {
                    // 同音字或形近字错误
                    const errorMap = poemErrorData[errorType];
                    const validChars = Object.keys(errorMap);

                    if (validChars.length > 0) {
                        const randomIndex = Math.floor(Math.random() * validChars.length);
                        const originalChar = validChars[randomIndex];
                        const errorChar = errorMap[originalChar];

                        // 查找字符在诗中的位置
                        const positions = [];
                        for (let j = 0; j < poem.content.length; j++) {
                            if (poem.content[j] === originalChar) {
                                positions.push(j);
                            }
                        }

                        if (positions.length > 0) {
                            // 随机选择一个位置
                            const positionIndex = Math.floor(Math.random() * positions.length);
                            const position = positions[positionIndex];

                            if (!usedPositions.has(position)) {
                                detectiveGame.errors.push({
                                    type: errorType === 'homophones' ? 'homophone' : 'similar',
                                    original: originalChar,
                                    error: errorChar,
                                    position: position
                                });
                                usedPositions.add(position);
                            }
                        }
                    }
                }
            }

            console.log('生成的错误：', detectiveGame.errors);
        }

        // 渲染诗歌（使用原版逻辑）
        function renderDetectiveGame() {
            const content = document.getElementById('detective-content');
            const poem = detectiveGame.currentPoem;

            // 复制诗歌内容用于修改
            let modifiedContent = poem.content;

            // 按错误位置降序排序，以便正确替换
            detectiveGame.errors.sort((a, b) => b.position - a.position);

            // 应用错误
            for (const error of detectiveGame.errors) {
                if (error.type === 'reverse') {
                    // 词序颠倒
                    modifiedContent = modifiedContent.substring(0, error.position) +
                                     error.error +
                                     modifiedContent.substring(error.position + error.original.length);
                } else {
                    // 同音字或形近字
                    modifiedContent = modifiedContent.substring(0, error.position) +
                                     error.error +
                                     modifiedContent.substring(error.position + 1);
                }
            }

            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-6 mb-6">
                    <h3 class="text-2xl font-bold text-center mb-2">${poem.title}</h3>
                    <p class="text-center text-gray-600 mb-4">${poem.author} · ${poem.grade}年级</p>
                    <div class="text-xl leading-relaxed text-center font-kai mb-4" id="poem-text-container">
                        <!-- 诗歌内容将通过renderPoemText函数生成 -->
                    </div>
                    <div class="text-center text-sm text-gray-600">
                        <p>点击你认为有错误的字符</p>
                    </div>
                </div>

                <div class="text-center mb-4">
                    <p class="text-lg mb-2">找出 <span class="font-bold text-red-600">${detectiveGame.errors.length}</span> 个错误</p>
                    <p class="text-sm text-gray-600">已找到: <span id="found-count" class="font-bold text-green-600">${detectiveGame.foundErrors.length}</span> 个</p>
                </div>

                <div class="flex justify-center gap-4 mb-4">
                    <button onclick="resetDetectiveGame()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                        重新开始
                    </button>
                    <button onclick="showDetectiveHint()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        提示
                    </button>
                </div>

                ${detectiveGame.foundErrors.length === detectiveGame.errors.length ? `
                    <div class="mt-6 p-4 bg-green-100 border border-green-300 rounded-lg text-center">
                        <h4 class="text-xl font-bold text-green-800 mb-2">🎉 恭喜你！</h4>
                        <p class="text-green-700 mb-4">你找出了所有的错误，真是个古诗小侦探！</p>
                        <button onclick="startDetectiveGame()" class="px-6 py-3 bg-green-500 text-white rounded-lg font-bold hover:bg-green-600">
                            再来一首
                        </button>
                    </div>
                ` : ''}
            `;

            // 渲染诗歌文本
            renderPoemText(modifiedContent);
        }

        // 渲染诗歌文本（使用原版逻辑）
        function renderPoemText(modifiedContent) {
            const poemTextContainer = document.getElementById('poem-text-container');
            poemTextContainer.innerHTML = '';

            // 分割成行（以逗号、句号、问号、感叹号为分隔符）
            const lines = modifiedContent.split(/[，。？！]/).filter(line => line.trim() !== '');
            const punctuation = modifiedContent.match(/[，。？！]/g) || [];

            // 渲染每一行
            lines.forEach((line, index) => {
                const lineElement = document.createElement('div');
                lineElement.className = 'text-center mb-2';

                // 渲染每个字符
                for (let i = 0; i < line.length; i++) {
                    const char = line[i];
                    const charElement = document.createElement('span');
                    charElement.textContent = char;
                    charElement.className = 'mx-1 cursor-pointer inline-block transition-all duration-200 hover:scale-110';

                    // 为错误字符添加点击事件
                    const position = modifiedContent.indexOf(line) + i;
                    const error = detectiveGame.errors.find(e => e.position === position);

                    if (error) {
                        // 检查是否已找到此错误
                        const found = detectiveGame.foundErrors.find(e => e.position === position);

                        if (found) {
                            // 已修复的错误
                            charElement.textContent = found.original;
                            charElement.className = 'mx-1 inline-block error-found';
                        } else {
                            // 未发现的错误
                            charElement.dataset.errorPosition = position;
                            charElement.addEventListener('click', () => selectDetectiveError(error));
                        }
                    }

                    lineElement.appendChild(charElement);
                }

                // 添加标点符号
                if (index < punctuation.length) {
                    const punctElement = document.createElement('span');
                    punctElement.textContent = punctuation[index];
                    punctElement.className = 'mx-1';
                    lineElement.appendChild(punctElement);
                }

                poemTextContainer.appendChild(lineElement);
            });
        }

        // 选择错误（使用原版逻辑）
        function selectDetectiveError(error) {
            detectiveGame.currentSelectedError = error;

            // 显示错误类型
            let typeText = '';
            switch (error.type) {
                case 'homophone':
                    typeText = '这是一个同音字错误';
                    break;
                case 'similar':
                    typeText = '这是一个形近字错误';
                    break;
                case 'reverse':
                    typeText = '这是一个词序颠倒错误';
                    break;
            }

            // 创建修复弹窗
            const fixModal = document.createElement('div');
            fixModal.id = 'detective-fix-modal';
            fixModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            fixModal.innerHTML = `
                <div class="bg-white rounded-xl p-6 max-w-md mx-4">
                    <div class="text-center mb-6">
                        <h3 class="text-2xl font-bold mb-2">发现错误！</h3>
                        <p class="text-lg text-gray-600">${typeText}</p>
                    </div>
                    <div class="mb-6">
                        <p class="text-lg font-bold mb-2">请选择正确的汉字：</p>
                        <div id="detective-fix-options" class="grid grid-cols-2 gap-3"></div>
                    </div>
                    <div class="flex justify-center">
                        <button onclick="closeDetectiveFixModal()" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-lg">
                            取消
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(fixModal);

            // 等待DOM元素创建完成后再添加选项
            setTimeout(() => {
                const fixOptions = document.getElementById('detective-fix-options');
                if (!fixOptions) {
                    console.error('找不到选项容器');
                    return;
                }

                // 创建选项数组
                const options = [
                    {
                        text: error.original,
                        isCorrect: true,
                        className: 'bg-green-500 hover:bg-green-600'
                    },
                    {
                        text: error.error,
                        isCorrect: false,
                        className: 'bg-red-500 hover:bg-red-600'
                    }
                ];

                // 随机打乱选项顺序
                options.sort(() => Math.random() - 0.5);

                // 创建按钮
                options.forEach(option => {
                    const button = document.createElement('button');
                    button.textContent = option.text;
                    button.className = `${option.className} text-white font-bold py-3 px-4 rounded-lg shadow-md transition-colors w-full`;
                    button.addEventListener('click', () => fixDetectiveError(option.isCorrect));
                    fixOptions.appendChild(button);
                });

                console.log('选项按钮已创建:', fixOptions.children.length);
            }, 100);
        }

        // 修复错误（使用原版逻辑）
        function fixDetectiveError(isCorrect) {
            if (isCorrect && detectiveGame.currentSelectedError) {
                // 添加到已修复错误列表
                detectiveGame.foundErrors.push(detectiveGame.currentSelectedError);

                // 播放正确音效
                playCorrectSound();

                // 检查是否所有错误都已修复
                if (detectiveGame.foundErrors.length === detectiveGame.errors.length) {
                    // 延迟显示胜利提示
                    setTimeout(() => {
                        alert('🎉 恭喜！你找出了所有错误！真是个古诗小侦探！');
                    }, 1000);
                }
            } else {
                // 播放错误音效
                playErrorSound();
                alert('选择错误，请再试一次！');
            }

            // 关闭修复弹窗
            closeDetectiveFixModal();
            detectiveGame.currentSelectedError = null;

            // 重新渲染游戏
            renderDetectiveGame();
        }

        // 关闭修复弹窗
        function closeDetectiveFixModal() {
            const modal = document.getElementById('detective-fix-modal');
            if (modal) {
                modal.remove();
            }
        }

        function resetDetectiveGame() {
            startDetectiveGame();
        }

        // 求助功能（使用原版逻辑）
        function showDetectiveHint() {
            // 找到一个未被发现的错误
            const remainingErrors = detectiveGame.errors.filter(e => !detectiveGame.foundErrors.some(fe => fe.position === e.position));

            if (remainingErrors.length > 0) {
                // 随机选择一个错误
                const randomIndex = Math.floor(Math.random() * remainingErrors.length);
                const errorToReveal = remainingErrors[randomIndex];

                let hintText = '';
                if (errorToReveal.type === 'reverse') {
                    hintText = `提示：诗中的"${errorToReveal.error}"词序有问题，正确的应该是"${errorToReveal.original}"`;
                } else {
                    hintText = `提示：诗中的"${errorToReveal.error}"字有问题，正确的应该是"${errorToReveal.original}"`;
                }

                alert(hintText);

                // 播放提示音效
                playHintSound();
            }
        }

        // 音效函数（简化版）
        function playCorrectSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(880, audioContext.currentTime + 0.3);

                gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5);

                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.5);
            } catch (e) {
                console.log('音效播放失败');
            }
        }

        function playErrorSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.type = 'sawtooth';
                oscillator.frequency.setValueAtTime(220, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(110, audioContext.currentTime + 0.3);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5);

                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.5);
            } catch (e) {
                console.log('音效播放失败');
            }
        }

        function playHintSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.type = 'triangle';
                oscillator.frequency.setValueAtTime(330, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(330, audioContext.currentTime + 0.1);
                oscillator.frequency.setValueAtTime(293.66, audioContext.currentTime + 0.2);

                gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3);

                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (e) {
                console.log('音效播放失败');
            }
        }

        // ========== 古诗默写助手游戏逻辑 ==========
        let reciteGame = {
            currentPoem: null,
            processedLines: [],
            isRevealed: false,
            timer: null,
            startTime: null
        };

        function startReciteGame() {
            if (selectedPoems.length === 0) {
                alert('请先选择古诗！');
                return;
            }

            // 随机选择一首诗
            reciteGame.currentPoem = selectedPoems[Math.floor(Math.random() * selectedPoems.length)];
            reciteGame.isRevealed = false;

            // 处理诗句挖空
            processPoemLines();

            // 渲染游戏界面
            renderReciteGame();

            // 开始计时
            startReciteTimer();
        }

        function processPoemLines() {
            const poem = reciteGame.currentPoem;
            const lines = poem.content.split(/([，。？！；,.?!;])/g).filter(line => line.trim() !== '');

            let processedLines = [];
            let currentLine = '';
            let currentPunctuation = '';

            lines.forEach((segment, index) => {
                // 判断是否为标点符号
                const isPunctuation = /[，。？！；,.?!;]/.test(segment);

                if (isPunctuation) {
                    currentPunctuation = segment;

                    // 处理当前行
                    const lineLength = currentLine.length;
                    const halfLength = Math.ceil(lineLength / 2);

                    // 随机选择前半部分或后半部分挖空
                    const shouldBlankFirstHalf = Math.random() > 0.5;
                    let blankedLine = '';
                    let answer = '';

                    if (shouldBlankFirstHalf) {
                        answer = currentLine.substring(0, halfLength);
                        blankedLine = '_'.repeat(halfLength) + currentLine.substring(halfLength);
                    } else {
                        answer = currentLine.substring(halfLength);
                        blankedLine = currentLine.substring(0, halfLength) + '_'.repeat(lineLength - halfLength);
                    }

                    processedLines.push({
                        original: currentLine + currentPunctuation,
                        blanked: blankedLine + currentPunctuation,
                        answer: answer,
                        blankPosition: shouldBlankFirstHalf ? 'first' : 'second',
                        blankLength: shouldBlankFirstHalf ? halfLength : lineLength - halfLength,
                        fullLineLength: lineLength,
                        punctuation: currentPunctuation
                    });

                    // 重置当前行和标点
                    currentLine = '';
                    currentPunctuation = '';
                } else {
                    currentLine += segment;
                }
            });

            reciteGame.processedLines = processedLines;
        }

        function renderReciteGame() {
            const content = document.getElementById('recite-content');
            const poem = reciteGame.currentPoem;

            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-6 mb-6">
                    <h3 class="text-2xl font-bold text-center mb-2">${poem.title}</h3>
                    <p class="text-center text-gray-600 mb-4">${poem.author} · ${poem.grade}年级</p>

                    <div class="text-center mb-4">
                        <div class="inline-flex items-center bg-blue-100 px-4 py-2 rounded-lg">
                            <i class="fa fa-clock-o mr-2 text-blue-600"></i>
                            <span id="recite-timer" class="font-bold text-blue-800">00:00</span>
                        </div>
                    </div>

                    <div class="space-y-4 leading-relaxed text-center font-kai text-xl" id="poem-lines">
                        ${reciteGame.isRevealed ? renderFullPoem() : renderBlankedPoem()}
                    </div>

                    <div class="text-center mt-6">
                        ${!reciteGame.isRevealed ? `
                            <p class="text-gray-600 mb-4">请在纸上默写完整的古诗，然后点击"查看答案"核对</p>
                            <div class="flex justify-center gap-4">
                                <button onclick="revealReciteAnswer()" class="px-6 py-3 bg-highlight text-white rounded-lg font-bold hover:bg-blue-600">
                                    <i class="fa fa-eye mr-2"></i>查看答案
                                </button>
                                <button onclick="startReciteGame()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                                    <i class="fa fa-refresh mr-2"></i>换一首
                                </button>
                            </div>
                        ` : `
                            <div class="mb-4 p-4 bg-blue-100 border border-blue-300 rounded-lg">
                                <h4 class="text-lg font-bold text-blue-800 mb-2">📝 答案已显示</h4>
                                <p class="text-blue-700">请对照你的默写内容，看看写对了多少！</p>
                            </div>
                            <div class="flex justify-center gap-4">
                                <button onclick="startReciteGame()" class="px-6 py-3 bg-green-500 text-white rounded-lg font-bold hover:bg-green-600">
                                    <i class="fa fa-refresh mr-2"></i>再来一首
                                </button>
                                <button onclick="hideReciteAnswer()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                                    <i class="fa fa-edit mr-2"></i>重新默写
                                </button>
                            </div>
                        `}
                    </div>
                </div>
            `;
        }

        function renderBlankedPoem() {
            return reciteGame.processedLines.map(line => {
                let lineHTML = '';
                if (line.blankPosition === 'first') {
                    // 前半部分挖空
                    lineHTML += `<span class="blank-section">${createBlankChars(line.blankLength)}</span>`;
                    lineHTML += line.blanked.substring(line.blankLength);
                } else {
                    // 后半部分挖空
                    lineHTML += line.blanked.substring(0, line.fullLineLength - line.blankLength);
                    lineHTML += `<span class="blank-section">${createBlankChars(line.blankLength)}</span>`;
                    lineHTML += line.punctuation;
                }
                return `<div class="poem-line">${lineHTML}</div>`;
            }).join('');
        }

        function renderFullPoem() {
            let html = '';
            reciteGame.processedLines.forEach((line, lineIndex) => {
                const answer = line.answer;
                let lineHTML = '';

                if (line.blankPosition === 'first') {
                    // 前半部分显示答案
                    lineHTML += `<span class="answer-reveal">${answer}</span>`;
                    lineHTML += line.blanked.substring(line.blankLength);
                } else {
                    // 后半部分显示答案
                    lineHTML += line.blanked.substring(0, line.fullLineLength - line.blankLength);
                    lineHTML += `<span class="answer-reveal">${answer}</span>`;
                    lineHTML += line.punctuation;
                }

                html += `<div class="poem-line reveal-animation" style="animation-delay: ${lineIndex * 0.3}s">${lineHTML}</div>`;
            });
            return html;
        }

        function createBlankChars(length) {
            let html = '';
            for (let i = 0; i < length; i++) {
                html += `<span class="blank-char"></span>`;
            }
            return html;
        }

        function startReciteTimer() {
            reciteGame.startTime = new Date();
            reciteGame.timer = setInterval(() => {
                const currentTime = new Date();
                const elapsedTime = Math.floor((currentTime - reciteGame.startTime) / 1000);

                const minutes = Math.floor(elapsedTime / 60).toString().padStart(2, '0');
                const seconds = (elapsedTime % 60).toString().padStart(2, '0');

                const timerEl = document.getElementById('recite-timer');
                if (timerEl) {
                    timerEl.textContent = `${minutes}:${seconds}`;
                }
            }, 1000);
        }

        function stopReciteTimer() {
            if (reciteGame.timer) {
                clearInterval(reciteGame.timer);
                reciteGame.timer = null;
            }
        }

        function revealReciteAnswer() {
            reciteGame.isRevealed = true;
            stopReciteTimer();
            renderReciteGame();
        }

        function hideReciteAnswer() {
            reciteGame.isRevealed = false;
            startReciteTimer();
            renderReciteGame();
        }

        // ========== 诗影寻踪游戏逻辑 ==========
        let shadowGame = {
            currentPoem: null,
            poemLines: [],
            shuffledLines: [],
            placedLines: [],
            timer: null,
            startTime: 0,
            elapsedTime: 0,
            isPlaying: false,
            isMemorizing: false
        };

        function startShadowGame() {
            if (selectedPoems.length === 0) {
                alert('请先选择古诗！');
                return;
            }

            // 重置游戏状态
            shadowGame = {
                currentPoem: null,
                poemLines: [],
                shuffledLines: [],
                placedLines: [],
                timer: null,
                startTime: 0,
                elapsedTime: 0,
                isPlaying: false,
                isMemorizing: false
            };

            // 随机选择一首诗
            const poem = selectedPoems[Math.floor(Math.random() * selectedPoems.length)];
            shadowGame.currentPoem = poem;

            // 将诗句按标点分割
            shadowGame.poemLines = poem.content.split(/[，。]/).filter(line => line.trim());
            shadowGame.shuffledLines = shuffleArray([...shadowGame.poemLines]);
            shadowGame.isPlaying = true;
            shadowGame.isMemorizing = true;

            // 渲染游戏界面
            renderShadowGame();

            // 5秒后开始游戏
            setTimeout(() => {
                shadowGame.isMemorizing = false;
                renderShadowGame();
                startShadowTimer();
                showNextShadowClue();
            }, 5000);
        }

        function renderShadowGame() {
            const content = document.getElementById('shadow-content');
            const poem = shadowGame.currentPoem;

            if (shadowGame.isMemorizing) {
                // 记忆阶段：显示完整诗句
                content.innerHTML = `
                    <div class="bg-poemBg rounded-lg p-6 mb-6">
                        <h3 class="text-2xl font-bold text-center mb-4">${poem.title} - ${poem.author}</h3>
                        <div class="text-center mb-4">
                            <div class="inline-flex items-center bg-blue-100 px-4 py-2 rounded-lg">
                                <i class="fa fa-eye mr-2 text-blue-600"></i>
                                <span class="font-bold text-blue-800">记忆时间：5秒</span>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            ${shadowGame.poemLines.map((line, index) => `
                                <div class="p-4 bg-white border rounded-lg text-center">
                                    <span class="text-xl font-kai text-gray-800">${line}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="text-center">
                            <p class="text-gray-600">请仔细记住这首诗的内容和顺序...</p>
                        </div>
                    </div>
                `;
            } else {
                // 游戏阶段：显示诗影和线索
                content.innerHTML = `
                    <div class="bg-poemBg rounded-lg p-6 mb-6">
                        <h3 class="text-2xl font-bold text-center mb-4">诗影寻踪</h3>

                        <div class="text-center mb-4">
                            <div class="inline-flex items-center bg-blue-100 px-4 py-2 rounded-lg">
                                <i class="fa fa-clock-o mr-2 text-blue-600"></i>
                                <span id="shadow-timer" class="font-bold text-blue-800">00:00</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6" id="poem-shadows">
                            ${shadowGame.poemLines.map((line, index) => {
                                const placed = shadowGame.placedLines.find(p => p.index === index);
                                return `
                                    <div class="poem-shadow p-4 bg-white border-2 border-dashed border-gray-300 rounded-lg text-center cursor-pointer hover:bg-gray-50 transition-colors"
                                         data-index="${index}" onclick="handleShadowClick(${index})">
                                        ${placed ?
                                            `<span class="text-xl font-kai text-green-800">${placed.line}</span>` :
                                            `<span class="text-gray-400">诗影 ${index + 1}</span>`
                                        }
                                    </div>
                                `;
                            }).join('')}
                        </div>

                        <div id="clue-container" class="mb-6 ${shadowGame.shuffledLines.length === 0 ? 'hidden' : ''}">
                            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
                                <h4 class="font-bold text-yellow-800 mb-2">🔍 当前线索：</h4>
                                <div id="clue-tile" class="text-xl font-kai text-yellow-700 cursor-pointer hover:bg-yellow-100 p-2 rounded">
                                    <!-- 线索将在这里显示 -->
                                </div>
                                <p class="text-sm text-yellow-600 mt-2">点击正确的诗影位置</p>
                            </div>
                        </div>

                        ${shadowGame.shuffledLines.length === 0 ? `
                            <div class="text-center p-4 bg-green-100 border border-green-300 rounded-lg">
                                <h4 class="text-xl font-bold text-green-800 mb-2">🎉 恭喜你！</h4>
                                <p class="text-green-700 mb-2">你成功完成了诗影寻踪！</p>
                                <p class="text-sm text-gray-600 mb-4">用时：<span id="final-time">${formatShadowTime(shadowGame.elapsedTime)}</span></p>
                                <button onclick="startShadowGame()" class="px-6 py-3 bg-green-500 text-white rounded-lg font-bold hover:bg-green-600">
                                    再来一首
                                </button>
                            </div>
                        ` : `
                            <div class="text-center">
                                <p class="text-gray-600 mb-4">已完成 ${shadowGame.placedLines.length} / ${shadowGame.poemLines.length} 句</p>
                                <button onclick="startShadowGame()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                                    重新开始
                                </button>
                            </div>
                        `}
                    </div>
                `;
            }
        }

        function shuffleArray(array) {
            const newArray = [...array];
            for (let i = newArray.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
            }
            return newArray;
        }

        function startShadowTimer() {
            shadowGame.startTime = Date.now();
            shadowGame.timer = setInterval(() => {
                const now = Date.now();
                shadowGame.elapsedTime = Math.floor((now - shadowGame.startTime) / 1000);
                const timerEl = document.getElementById('shadow-timer');
                if (timerEl) {
                    timerEl.textContent = formatShadowTime(shadowGame.elapsedTime);
                }
            }, 1000);
        }

        function stopShadowTimer() {
            if (shadowGame.timer) {
                clearInterval(shadowGame.timer);
                shadowGame.timer = null;
            }
        }

        function formatShadowTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        function showNextShadowClue() {
            if (shadowGame.shuffledLines.length === 0) {
                // 游戏胜利
                stopShadowTimer();
                renderShadowGame();
                return;
            }

            const clueIndex = Math.floor(Math.random() * shadowGame.shuffledLines.length);
            const clueLine = shadowGame.shuffledLines[clueIndex];

            const clueTile = document.getElementById('clue-tile');
            if (clueTile) {
                clueTile.textContent = clueLine;
                clueTile.dataset.line = clueLine;
                clueTile.dataset.index = clueIndex;
            }

            // 从shuffledLines中移除当前线索
            shadowGame.shuffledLines.splice(clueIndex, 1);
        }

        function handleShadowClick(shadowIndex) {
            if (!shadowGame.isPlaying || shadowGame.isMemorizing) return;

            const clueTile = document.getElementById('clue-tile');
            if (!clueTile) return;

            const clueLine = clueTile.dataset.line;

            if (shadowGame.poemLines[shadowIndex] === clueLine) {
                // 放置正确
                shadowGame.placedLines.push({ index: shadowIndex, line: clueLine });

                // 重新渲染
                renderShadowGame();

                // 显示下一个线索
                setTimeout(() => {
                    showNextShadowClue();
                }, 300);
            } else {
                // 放置错误
                const shadowEl = document.querySelector(`[data-index="${shadowIndex}"]`);
                if (shadowEl) {
                    shadowEl.classList.add('bg-red-100', 'border-red-300');
                    setTimeout(() => {
                        shadowEl.classList.remove('bg-red-100', 'border-red-300');
                    }, 500);
                }
            }
        }

        // ========== 连句成诗游戏逻辑 ==========
        let connectGame = {
            currentPoem: null,
            currentLineIndex: 0,
            timer: null,
            startTime: null,
            elapsedSeconds: 0
        };

        function startConnectGame() {
            if (selectedPoems.length === 0) {
                alert('请先选择古诗！');
                return;
            }

            // 重置游戏状态
            connectGame.elapsedSeconds = 0;

            // 随机选择一首诗
            connectGame.currentPoem = selectedPoems[Math.floor(Math.random() * selectedPoems.length)];
            connectGame.currentLineIndex = 0;

            // 渲染游戏界面
            renderConnectGame();

            // 开始计时
            startConnectTimer();
        }

        function renderConnectGame() {
            const content = document.getElementById('connect-content');
            const poem = connectGame.currentPoem;
            const lines = poem.content.split(/[，。]/).filter(line => line.trim());

            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-6 mb-6">
                    <h3 class="text-2xl font-bold text-center mb-2">${poem.title} - ${poem.author}</h3>

                    <div class="text-center mb-4">
                        <div class="inline-flex items-center bg-blue-100 px-4 py-2 rounded-lg">
                            <i class="fa fa-clock-o mr-2 text-blue-600"></i>
                            <span id="connect-timer" class="font-bold text-blue-800">00:00</span>
                        </div>
                    </div>

                    <div class="mb-8 bg-white rounded-lg p-6 min-h-[180px] flex flex-col items-center justify-center" id="poem-display">
                        <div class="space-y-4 w-full text-center">
                            ${lines.slice(0, connectGame.currentLineIndex + 1).map((line, index) => `
                                <div class="poem-line text-2xl font-medium text-primary">
                                    ${line}${index < lines.length - 1 ? '，' : '。'}
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    ${connectGame.currentLineIndex >= lines.length - 1 ? `
                        <div class="text-center p-4 bg-green-100 border border-green-300 rounded-lg">
                            <h4 class="text-xl font-bold text-green-800 mb-2">🎉 恭喜你！</h4>
                            <p class="text-green-700 mb-2">你成功完成了整首诗！</p>
                            <p class="text-sm text-gray-600 mb-4">用时：${formatConnectTime(connectGame.elapsedSeconds)}</p>
                            <button onclick="startConnectGame()" class="px-6 py-3 bg-green-500 text-white rounded-lg font-bold hover:bg-green-600">
                                再来一首
                            </button>
                        </div>
                    ` : `
                        <div id="options-container" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            ${generateConnectOptions(lines).map(option => `
                                <button onclick="handleConnectOption('${option.text}', ${option.isCorrect})"
                                        class="option-btn bg-secondary hover:bg-secondary/80 text-white font-semibold py-5 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg text-xl">
                                    ${option.text}
                                </button>
                            `).join('')}
                        </div>

                        <div class="text-center">
                            <p class="text-gray-600 mb-4">选择正确的下一句 (${connectGame.currentLineIndex + 2}/${lines.length})</p>
                            <button onclick="startConnectGame()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                                重新开始
                            </button>
                        </div>
                    `}
                </div>
            `;
        }

        function generateConnectOptions(lines) {
            const nextLineIndex = connectGame.currentLineIndex + 1;
            if (nextLineIndex >= lines.length) return [];

            const correctLine = lines[nextLineIndex];
            const allLines = [];

            // 收集所有诗歌的所有诗句
            selectedPoems.forEach(poem => {
                const poemLines = poem.content.split(/[，。]/).filter(line => line.trim());
                poemLines.forEach(line => {
                    allLines.push(line);
                });
            });

            // 过滤掉当前诗歌中已经出现的诗句
            const availableLines = allLines.filter(line => {
                return !lines.slice(0, nextLineIndex + 1).includes(line) && line !== correctLine;
            });

            // 随机选择2个干扰项
            const wrongOptions = [];
            const shuffledAvailable = [...availableLines].sort(() => Math.random() - 0.5);
            for (let i = 0; i < Math.min(2, shuffledAvailable.length); i++) {
                wrongOptions.push(shuffledAvailable[i]);
            }

            // 组合选项并随机排序
            const options = [
                { text: correctLine, isCorrect: true },
                ...wrongOptions.map(text => ({ text, isCorrect: false }))
            ];

            return options.sort(() => Math.random() - 0.5);
        }

        function handleConnectOption(selectedLine, isCorrect) {
            // 禁用所有选项按钮
            document.querySelectorAll('#options-container button').forEach(btn => {
                btn.disabled = true;
            });

            const button = event.target;

            if (isCorrect) {
                button.classList.add('bg-green-500');

                // 延迟添加下一句
                setTimeout(() => {
                    connectGame.currentLineIndex++;
                    renderConnectGame();
                }, 800);
            } else {
                button.classList.add('bg-red-500');

                // 高亮显示正确答案
                document.querySelectorAll('#options-container button').forEach(btn => {
                    const poem = connectGame.currentPoem;
                    const lines = poem.content.split(/[，。]/).filter(line => line.trim());
                    const correctLine = lines[connectGame.currentLineIndex + 1];
                    if (btn.textContent.trim() === correctLine) {
                        btn.classList.add('bg-green-500');
                    }
                });

                // 延迟添加下一句
                setTimeout(() => {
                    connectGame.currentLineIndex++;
                    renderConnectGame();
                }, 1500);
            }
        }

        function startConnectTimer() {
            connectGame.startTime = new Date();
            connectGame.timer = setInterval(() => {
                connectGame.elapsedSeconds++;
                const timerEl = document.getElementById('connect-timer');
                if (timerEl) {
                    timerEl.textContent = formatConnectTime(connectGame.elapsedSeconds);
                }
            }, 1000);
        }

        function stopConnectTimer() {
            if (connectGame.timer) {
                clearInterval(connectGame.timer);
                connectGame.timer = null;
            }
        }

        function formatConnectTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    </script>
</body>
</html>
