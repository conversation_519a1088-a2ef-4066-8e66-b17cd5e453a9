<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>古诗学习乐园 - 四合一游戏平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        parchment: '#f9f5f0',
                        brown: '#6d5b4b',
                        gold: '#d4af37',
                        green: '#2e7d32',
                        red: '#c62828',
                        paper: '#fdfaf3',
                        primary: '#6a5a4a',
                        highlight: '#5a8b82',
                        poemBg: '#f8f4ea',
                        background: '#f5f1e8',
                        secondary: '#8b7355',
                        accent: '#d4af37'
                    },
                    fontFamily: {
                        song: ['"Noto Serif SC"', 'serif'],
                        kai: ['KaiTi', 'STKaiti', 'Sim<PERSON><PERSON>', 'serif']
                    }
                }
            }
        }
    </script>

    <style>
        /* 自定义样式 */
        .tab-active {
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
        }

        .poem-selector {
            transition: all 0.3s ease;
        }

        .poem-selector.collapsed {
            max-height: 60px;
            overflow: hidden;
        }

        .poem-selector.expanded {
            max-height: 500px;
        }

        .game-content {
            min-height: 600px;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .poem-card {
            transition: all 0.2s ease;
        }

        .poem-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .poem-card.selected {
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            color: white;
        }
    </style>
</head>
<body class="bg-parchment min-h-screen font-song text-brown">
    <!-- 页眉 -->
    <header class="bg-gradient-to-r from-gold to-yellow-400 text-white p-6 shadow-lg">
        <div class="container mx-auto text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-2">
                <i class="fa fa-book mr-3"></i>古诗学习乐园
            </h1>
            <p class="text-xl opacity-90">四合一游戏平台，让学习古诗更有趣！</p>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-6">
        <!-- 游戏导航标签 -->
        <div class="bg-white rounded-xl shadow-lg p-4 mb-6">
            <div class="flex flex-wrap justify-center gap-2 md:gap-4">
                <button id="tab-detective" class="tab-btn tab-active px-6 py-3 rounded-lg font-bold transition-all duration-300">
                    <i class="fa fa-search mr-2"></i>古诗小侦探
                </button>
                <button id="tab-recite" class="tab-btn px-6 py-3 rounded-lg font-bold transition-all duration-300 bg-gray-100 hover:bg-gray-200">
                    <i class="fa fa-pencil mr-2"></i>古诗默写助手
                </button>
                <button id="tab-shadow" class="tab-btn px-6 py-3 rounded-lg font-bold transition-all duration-300 bg-gray-100 hover:bg-gray-200">
                    <i class="fa fa-eye mr-2"></i>诗影寻踪
                </button>
                <button id="tab-connect" class="tab-btn px-6 py-3 rounded-lg font-bold transition-all duration-300 bg-gray-100 hover:bg-gray-200">
                    <i class="fa fa-link mr-2"></i>连句成诗
                </button>
            </div>
        </div>

        <!-- 古诗选择面板 -->
        <div id="poem-selector" class="poem-selector collapsed bg-white rounded-xl shadow-lg mb-6">
            <div class="p-4 border-b cursor-pointer" onclick="togglePoemSelector()">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold text-primary">
                        <i class="fa fa-list mr-2"></i>古诗选择
                        <span id="selected-count" class="text-sm text-gray-500 ml-2">(已选择: 0首)</span>
                    </h3>
                    <i id="selector-arrow" class="fa fa-chevron-down transition-transform duration-300"></i>
                </div>
            </div>

            <div id="selector-content" class="p-4">
                <!-- 筛选区域 -->
                <div class="flex flex-wrap gap-4 mb-4">
                    <select id="grade-filter" class="px-3 py-2 border rounded-lg">
                        <option value="">所有年级</option>
                        <option value="1">一年级</option>
                        <option value="2">二年级</option>
                        <option value="3">三年级</option>
                        <option value="4">四年级</option>
                        <option value="5">五年级</option>
                        <option value="6">六年级</option>
                    </select>

                    <select id="author-filter" class="px-3 py-2 border rounded-lg">
                        <option value="">所有作者</option>
                    </select>

                    <button id="select-all-btn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        全选
                    </button>

                    <button id="random-select-btn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                        随机选择5首
                    </button>
                </div>

                <!-- 古诗列表 -->
                <div id="poems-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
                    <!-- 古诗卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 确认按钮 -->
                <div class="mt-4 text-center">
                    <button id="confirm-selection-btn" class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600 transition-colors">
                        确认选择并开始游戏
                    </button>
                </div>
            </div>
        </div>

        <!-- 游戏内容区域 -->
        <div id="game-area" class="game-content">
            <!-- 古诗小侦探 -->
            <div id="game-detective" class="game-panel fade-in">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-2">
                            <i class="fa fa-search mr-2"></i>古诗小侦探
                        </h2>
                        <p class="text-gray-600">找出古诗中的错别字，成为古诗小侦探！</p>
                    </div>

                    <div id="detective-content" class="text-center">
                        <div class="bg-poemBg rounded-lg p-8 mb-6">
                            <p class="text-lg text-gray-600 mb-4">请先选择古诗，然后开始游戏</p>
                            <button class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600" onclick="togglePoemSelector()">
                                选择古诗
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 古诗默写助手 -->
            <div id="game-recite" class="game-panel hidden">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-2">
                            <i class="fa fa-pencil mr-2"></i>古诗默写助手
                        </h2>
                        <p class="text-gray-600">线上出题，线下书写，即时核对</p>
                    </div>

                    <div id="recite-content" class="text-center">
                        <div class="bg-poemBg rounded-lg p-8 mb-6">
                            <p class="text-lg text-gray-600 mb-4">请先选择古诗，然后开始默写练习</p>
                            <button class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600" onclick="togglePoemSelector()">
                                选择古诗
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 诗影寻踪 -->
            <div id="game-shadow" class="game-panel hidden">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-2">
                            <i class="fa fa-eye mr-2"></i>诗影寻踪
                        </h2>
                        <p class="text-gray-600">根据提示猜古诗，锻炼理解能力</p>
                    </div>

                    <div id="shadow-content" class="text-center">
                        <div class="bg-poemBg rounded-lg p-8 mb-6">
                            <p class="text-lg text-gray-600 mb-4">请先选择古诗，然后开始猜诗游戏</p>
                            <button class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600" onclick="togglePoemSelector()">
                                选择古诗
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 连句成诗 -->
            <div id="game-connect" class="game-panel hidden">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-2">
                            <i class="fa fa-link mr-2"></i>连句成诗
                        </h2>
                        <p class="text-gray-600">将打乱的诗句重新排序，完成古诗</p>
                    </div>

                    <div id="connect-content" class="text-center">
                        <div class="bg-poemBg rounded-lg p-8 mb-6">
                            <p class="text-lg text-gray-600 mb-4">请先选择古诗，然后开始排序游戏</p>
                            <button class="px-6 py-3 bg-gold text-white rounded-lg font-bold hover:bg-yellow-600" onclick="togglePoemSelector()">
                                选择古诗
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white p-6 mt-8">
        <div class="container mx-auto text-center">
            <p class="text-lg mb-2">古诗学习乐园 &copy; 2025 | 让学习古诗更有趣</p>
            <div class="text-gray-400 text-sm">
                <span class="mx-2">四合一游戏平台</span>
                <span class="mx-2">|</span>
                <span class="mx-2">寓教于乐</span>
                <span class="mx-2">|</span>
                <span class="mx-2">传承经典</span>
            </div>
        </div>
    </footer>

    <!-- 引入古诗数据 -->
    <script src="js/poems-data.js"></script>

    <!-- 主要JavaScript逻辑 -->
    <script>
        // 全局变量
        let selectedPoems = [];
        let currentGame = 'detective';
        let allPoems = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');
            initializePage();
        });

        // 初始化页面
        function initializePage() {
            // 检查古诗数据是否加载
            if (typeof poemDatabase === 'undefined') {
                console.error('古诗数据未加载！');
                return;
            }

            allPoems = poemDatabase;
            console.log('加载了', allPoems.length, '首古诗');

            // 初始化古诗选择面板
            initializePoemSelector();

            // 初始化标签页切换
            initializeTabs();

            // 默认选择一些古诗
            selectRandomPoems(5);
        }

        // 初始化古诗选择面板
        function initializePoemSelector() {
            // 填充作者下拉框
            const authorFilter = document.getElementById('author-filter');
            const authors = [...new Set(allPoems.map(poem => poem.author))].sort();
            authors.forEach(author => {
                const option = document.createElement('option');
                option.value = author;
                option.textContent = author;
                authorFilter.appendChild(option);
            });

            // 渲染古诗网格
            renderPoemsGrid();

            // 绑定事件
            document.getElementById('grade-filter').addEventListener('change', filterPoems);
            document.getElementById('author-filter').addEventListener('change', filterPoems);
            document.getElementById('select-all-btn').addEventListener('click', selectAllPoems);
            document.getElementById('random-select-btn').addEventListener('click', () => selectRandomPoems(5));
            document.getElementById('confirm-selection-btn').addEventListener('click', confirmSelection);
        }

        // 渲染古诗网格
        function renderPoemsGrid(poemsToShow = allPoems) {
            const grid = document.getElementById('poems-grid');
            grid.innerHTML = '';

            poemsToShow.forEach((poem, index) => {
                const card = document.createElement('div');
                card.className = 'poem-card p-3 border rounded-lg cursor-pointer';
                card.innerHTML = `
                    <div class="font-bold text-sm mb-1">${poem.title}</div>
                    <div class="text-xs text-gray-600">${poem.author} · ${poem.grade}年级</div>
                `;

                // 检查是否已选择
                if (selectedPoems.some(p => p.title === poem.title)) {
                    card.classList.add('selected');
                }

                card.addEventListener('click', () => togglePoemSelection(poem, card));
                grid.appendChild(card);
            });
        }

        // 切换古诗选择状态
        function togglePoemSelection(poem, cardElement) {
            const index = selectedPoems.findIndex(p => p.title === poem.title);

            if (index > -1) {
                // 取消选择
                selectedPoems.splice(index, 1);
                cardElement.classList.remove('selected');
            } else {
                // 添加选择
                selectedPoems.push(poem);
                cardElement.classList.add('selected');
            }

            updateSelectedCount();
        }

        // 更新已选择数量显示
        function updateSelectedCount() {
            document.getElementById('selected-count').textContent = `(已选择: ${selectedPoems.length}首)`;
        }

        // 筛选古诗
        function filterPoems() {
            const gradeFilter = document.getElementById('grade-filter').value;
            const authorFilter = document.getElementById('author-filter').value;

            let filteredPoems = allPoems;

            if (gradeFilter) {
                filteredPoems = filteredPoems.filter(poem => poem.grade === gradeFilter);
            }

            if (authorFilter) {
                filteredPoems = filteredPoems.filter(poem => poem.author === authorFilter);
            }

            renderPoemsGrid(filteredPoems);
        }

        // 全选古诗
        function selectAllPoems() {
            const gradeFilter = document.getElementById('grade-filter').value;
            const authorFilter = document.getElementById('author-filter').value;

            let poemsToSelect = allPoems;

            if (gradeFilter) {
                poemsToSelect = poemsToSelect.filter(poem => poem.grade === gradeFilter);
            }

            if (authorFilter) {
                poemsToSelect = poemsToSelect.filter(poem => poem.author === authorFilter);
            }

            selectedPoems = [...poemsToSelect];
            updateSelectedCount();
            renderPoemsGrid(poemsToSelect);
        }

        // 随机选择古诗
        function selectRandomPoems(count) {
            const shuffled = [...allPoems].sort(() => 0.5 - Math.random());
            selectedPoems = shuffled.slice(0, count);
            updateSelectedCount();
            renderPoemsGrid();
        }

        // 确认选择
        function confirmSelection() {
            if (selectedPoems.length === 0) {
                alert('请至少选择一首古诗！');
                return;
            }

            // 折叠选择面板
            togglePoemSelector();

            // 初始化当前游戏
            initializeCurrentGame();

            alert(`已选择 ${selectedPoems.length} 首古诗，开始游戏！`);
        }

        // 切换古诗选择面板
        function togglePoemSelector() {
            const selector = document.getElementById('poem-selector');
            const arrow = document.getElementById('selector-arrow');

            if (selector.classList.contains('collapsed')) {
                selector.classList.remove('collapsed');
                selector.classList.add('expanded');
                arrow.style.transform = 'rotate(180deg)';
            } else {
                selector.classList.remove('expanded');
                selector.classList.add('collapsed');
                arrow.style.transform = 'rotate(0deg)';
            }
        }

        // 初始化标签页切换
        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-btn');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const gameId = button.id.replace('tab-', '');
                    switchToGame(gameId);
                });
            });
        }

        // 切换游戏
        function switchToGame(gameId) {
            // 更新当前游戏
            currentGame = gameId;

            // 更新标签按钮样式
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('tab-active');
                btn.classList.add('bg-gray-100', 'hover:bg-gray-200');
            });

            const activeTab = document.getElementById(`tab-${gameId}`);
            activeTab.classList.add('tab-active');
            activeTab.classList.remove('bg-gray-100', 'hover:bg-gray-200');

            // 隐藏所有游戏面板
            document.querySelectorAll('.game-panel').forEach(panel => {
                panel.classList.add('hidden');
            });

            // 显示当前游戏面板
            const currentPanel = document.getElementById(`game-${gameId}`);
            currentPanel.classList.remove('hidden');
            currentPanel.classList.add('fade-in');

            // 初始化当前游戏
            initializeCurrentGame();
        }

        // 初始化当前游戏
        function initializeCurrentGame() {
            if (selectedPoems.length === 0) {
                return;
            }

            console.log(`初始化游戏: ${currentGame}, 古诗数量: ${selectedPoems.length}`);

            // 根据不同游戏调用不同的初始化函数
            switch (currentGame) {
                case 'detective':
                    initializeDetectiveGame();
                    break;
                case 'recite':
                    initializeReciteGame();
                    break;
                case 'shadow':
                    initializeShadowGame();
                    break;
                case 'connect':
                    initializeConnectGame();
                    break;
            }
        }

        // 初始化古诗小侦探游戏
        function initializeDetectiveGame() {
            const content = document.getElementById('detective-content');
            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-8">
                    <h3 class="text-xl font-bold mb-4">古诗小侦探已准备就绪！</h3>
                    <p class="text-gray-600 mb-4">已加载 ${selectedPoems.length} 首古诗</p>
                    <button class="px-6 py-3 bg-red text-white rounded-lg font-bold hover:bg-red-600" onclick="startDetectiveGame()">
                        开始找错字
                    </button>
                </div>
            `;
        }

        // 初始化古诗默写助手游戏
        function initializeReciteGame() {
            const content = document.getElementById('recite-content');
            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-8">
                    <h3 class="text-xl font-bold mb-4">古诗默写助手已准备就绪！</h3>
                    <p class="text-gray-600 mb-4">已加载 ${selectedPoems.length} 首古诗</p>
                    <button class="px-6 py-3 bg-highlight text-white rounded-lg font-bold hover:bg-blue-600" onclick="startReciteGame()">
                        开始默写练习
                    </button>
                </div>
            `;
        }

        // 初始化诗影寻踪游戏
        function initializeShadowGame() {
            const content = document.getElementById('shadow-content');
            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-8">
                    <h3 class="text-xl font-bold mb-4">诗影寻踪已准备就绪！</h3>
                    <p class="text-gray-600 mb-4">已加载 ${selectedPoems.length} 首古诗</p>
                    <button class="px-6 py-3 bg-green text-white rounded-lg font-bold hover:bg-green-600" onclick="startShadowGame()">
                        开始猜诗游戏
                    </button>
                </div>
            `;
        }

        // 初始化连句成诗游戏
        function initializeConnectGame() {
            const content = document.getElementById('connect-content');
            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-8">
                    <h3 class="text-xl font-bold mb-4">连句成诗已准备就绪！</h3>
                    <p class="text-gray-600 mb-4">已加载 ${selectedPoems.length} 首古诗</p>
                    <button class="px-6 py-3 bg-accent text-white rounded-lg font-bold hover:bg-yellow-600" onclick="startConnectGame()">
                        开始排序游戏
                    </button>
                </div>
            `;
        }

        // ========== 古诗小侦探游戏逻辑 ==========
        let detectiveGame = {
            currentPoem: null,
            errors: [],
            foundErrors: [],
            currentSelectedError: null
        };

        function startDetectiveGame() {
            if (selectedPoems.length === 0) {
                alert('请先选择古诗！');
                return;
            }

            // 随机选择一首诗
            detectiveGame.currentPoem = selectedPoems[Math.floor(Math.random() * selectedPoems.length)];
            detectiveGame.errors = [];
            detectiveGame.foundErrors = [];
            detectiveGame.currentSelectedError = null;

            // 生成错误
            generateDetectiveErrors();

            // 渲染游戏界面
            renderDetectiveGame();
        }

        function generateDetectiveErrors() {
            const poem = detectiveGame.currentPoem;
            const content = poem.content;

            // 预定义的错误类型
            const errorTypes = {
                homophones: { "莺": "鹰", "醉": "最", "鸢": "鸳", "妆": "装", "绦": "涛", "裁": "才", "离": "梨", "岁": "穗", "尽": "进" },
                similar: { "堤": "提", "杨": "扬", "碧": "壁", "绦": "条", "剪": "箭", "原": "园", "荣": "菜", "烧": "浇" }
            };

            // 随机生成2-4个错误
            const errorCount = Math.floor(Math.random() * 3) + 2;
            const usedPositions = new Set();

            for (let i = 0; i < errorCount && detectiveGame.errors.length < 4; i++) {
                // 随机选择错误类型
                const errorType = Math.random() > 0.5 ? 'homophones' : 'similar';
                const errorMap = errorTypes[errorType];

                // 找到可以替换的字符
                const availableChars = Object.keys(errorMap).filter(char =>
                    content.includes(char) && !usedPositions.has(content.indexOf(char))
                );

                if (availableChars.length > 0) {
                    const char = availableChars[Math.floor(Math.random() * availableChars.length)];
                    const position = content.indexOf(char);

                    if (!usedPositions.has(position)) {
                        detectiveGame.errors.push({
                            position: position,
                            original: char,
                            wrong: errorMap[char],
                            type: errorType
                        });
                        usedPositions.add(position);
                    }
                }
            }
        }

        function renderDetectiveGame() {
            const content = document.getElementById('detective-content');
            const poem = detectiveGame.currentPoem;

            // 创建带错误的内容
            let modifiedContent = poem.content;
            detectiveGame.errors.forEach(error => {
                modifiedContent = modifiedContent.replace(error.original, error.wrong);
            });

            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-6 mb-6">
                    <h3 class="text-2xl font-bold text-center mb-2">${poem.title}</h3>
                    <p class="text-center text-gray-600 mb-4">${poem.author} · ${poem.grade}年级</p>
                    <div class="text-xl leading-relaxed text-center font-kai" id="poem-text">
                        ${renderPoemWithErrors(modifiedContent)}
                    </div>
                </div>

                <div class="text-center mb-4">
                    <p class="text-lg mb-2">找出 <span class="font-bold text-red">${detectiveGame.errors.length}</span> 个错误</p>
                    <p class="text-sm text-gray-600">已找到: <span id="found-count">${detectiveGame.foundErrors.length}</span> 个</p>
                </div>

                <div class="flex justify-center gap-4">
                    <button onclick="resetDetectiveGame()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                        重新开始
                    </button>
                    <button onclick="showDetectiveHint()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        提示
                    </button>
                </div>

                ${detectiveGame.foundErrors.length === detectiveGame.errors.length ? `
                    <div class="mt-6 p-4 bg-green-100 border border-green-300 rounded-lg text-center">
                        <h4 class="text-xl font-bold text-green-800 mb-2">🎉 恭喜你！</h4>
                        <p class="text-green-700">你找出了所有的错误，真是个古诗小侦探！</p>
                        <button onclick="startDetectiveGame()" class="mt-3 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                            再来一首
                        </button>
                    </div>
                ` : ''}
            `;
        }

        function renderPoemWithErrors(content) {
            let result = '';
            for (let i = 0; i < content.length; i++) {
                const char = content[i];
                const error = detectiveGame.errors.find(e => e.position === i);
                const found = detectiveGame.foundErrors.find(e => e.position === i);

                if (error) {
                    if (found) {
                        // 已找到的错误，显示正确字符
                        result += `<span class="bg-green-200 text-green-800 px-1 rounded cursor-pointer">${error.original}</span>`;
                    } else {
                        // 未找到的错误，可点击
                        result += `<span class="cursor-pointer hover:bg-yellow-200 px-1 rounded transition-colors" onclick="selectDetectiveError(${i})">${char}</span>`;
                    }
                } else {
                    result += char;
                }
            }
            return result;
        }

        function selectDetectiveError(position) {
            const error = detectiveGame.errors.find(e => e.position === position);
            if (!error || detectiveGame.foundErrors.find(e => e.position === position)) {
                return;
            }

            // 标记为已找到
            detectiveGame.foundErrors.push(error);

            // 重新渲染
            renderDetectiveGame();

            // 检查是否完成
            if (detectiveGame.foundErrors.length === detectiveGame.errors.length) {
                setTimeout(() => {
                    alert('🎉 恭喜！你找出了所有错误！');
                }, 500);
            }
        }

        function resetDetectiveGame() {
            startDetectiveGame();
        }

        function showDetectiveHint() {
            const remainingErrors = detectiveGame.errors.filter(e =>
                !detectiveGame.foundErrors.find(f => f.position === e.position)
            );

            if (remainingErrors.length > 0) {
                const hint = remainingErrors[0];
                alert(`提示：第 ${hint.position + 1} 个字符有问题，正确的应该是"${hint.original}"`);
            }
        }

        // ========== 古诗默写助手游戏逻辑 ==========
        let reciteGame = {
            currentPoem: null,
            blankedContent: '',
            originalContent: '',
            isRevealed: false
        };

        function startReciteGame() {
            if (selectedPoems.length === 0) {
                alert('请先选择古诗！');
                return;
            }

            // 随机选择一首诗
            reciteGame.currentPoem = selectedPoems[Math.floor(Math.random() * selectedPoems.length)];
            reciteGame.isRevealed = false;

            // 生成填空版本
            generateBlankedPoem();

            // 渲染游戏界面
            renderReciteGame();
        }

        function generateBlankedPoem() {
            const poem = reciteGame.currentPoem;
            const content = poem.content;
            reciteGame.originalContent = content;

            // 随机选择要隐藏的字符（约30-50%的字符）
            const chars = content.split('');
            const blankCount = Math.floor(chars.length * (0.3 + Math.random() * 0.2));
            const positions = new Set();

            // 随机选择位置，但避免标点符号
            while (positions.size < blankCount) {
                const pos = Math.floor(Math.random() * chars.length);
                const char = chars[pos];
                if (char !== '，' && char !== '。' && char !== '、' && char !== '；' && char !== '：') {
                    positions.add(pos);
                }
            }

            // 创建填空版本
            reciteGame.blankedContent = chars.map((char, index) => {
                return positions.has(index) ? '_' : char;
            }).join('');
        }

        function renderReciteGame() {
            const content = document.getElementById('recite-content');
            const poem = reciteGame.currentPoem;

            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-6 mb-6">
                    <h3 class="text-2xl font-bold text-center mb-2">${poem.title}</h3>
                    <p class="text-center text-gray-600 mb-4">${poem.author} · ${poem.grade}年级</p>
                    <div class="text-xl leading-relaxed text-center font-kai mb-6" id="poem-text">
                        ${reciteGame.isRevealed ?
                            `<div class="text-green-800">${reciteGame.originalContent}</div>` :
                            `<div>${formatBlankedPoem(reciteGame.blankedContent)}</div>`
                        }
                    </div>

                    ${!reciteGame.isRevealed ? `
                        <div class="text-center mb-4">
                            <p class="text-lg text-gray-600 mb-4">请在纸上默写完整的古诗，然后点击"查看答案"核对</p>
                            <div class="flex justify-center gap-4">
                                <button onclick="revealReciteAnswer()" class="px-6 py-3 bg-highlight text-white rounded-lg font-bold hover:bg-blue-600">
                                    查看答案
                                </button>
                                <button onclick="startReciteGame()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                                    换一首
                                </button>
                            </div>
                        </div>
                    ` : `
                        <div class="text-center">
                            <div class="mb-4 p-4 bg-blue-100 border border-blue-300 rounded-lg">
                                <h4 class="text-lg font-bold text-blue-800 mb-2">📝 答案已显示</h4>
                                <p class="text-blue-700">请对照你的默写内容，看看写对了多少！</p>
                            </div>
                            <div class="flex justify-center gap-4">
                                <button onclick="startReciteGame()" class="px-6 py-3 bg-green-500 text-white rounded-lg font-bold hover:bg-green-600">
                                    再来一首
                                </button>
                                <button onclick="hideReciteAnswer()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                                    重新默写
                                </button>
                            </div>
                        </div>
                    `}
                </div>
            `;
        }

        function formatBlankedPoem(content) {
            return content.replace(/_/g, '<span class="inline-block w-6 h-8 border-b-2 border-gray-400 mx-1"></span>');
        }

        function revealReciteAnswer() {
            reciteGame.isRevealed = true;
            renderReciteGame();
        }

        function hideReciteAnswer() {
            reciteGame.isRevealed = false;
            renderReciteGame();
        }

        // ========== 诗影寻踪游戏逻辑 ==========
        let shadowGame = {
            currentPoem: null,
            revealedLines: [],
            isComplete: false,
            currentClue: ''
        };

        function startShadowGame() {
            if (selectedPoems.length === 0) {
                alert('请先选择古诗！');
                return;
            }

            // 随机选择一首诗
            shadowGame.currentPoem = selectedPoems[Math.floor(Math.random() * selectedPoems.length)];
            shadowGame.revealedLines = [];
            shadowGame.isComplete = false;

            // 生成线索
            generateShadowClue();

            // 渲染游戏界面
            renderShadowGame();
        }

        function generateShadowClue() {
            const poem = shadowGame.currentPoem;
            const clues = [
                `这首诗的作者是${poem.author}`,
                `这是一首适合${poem.grade}年级学习的古诗`,
                `诗的标题是《${poem.title}》`,
                `这首诗的第一句是："${poem.content.split('，')[0]}"`
            ];

            shadowGame.currentClue = clues[Math.floor(Math.random() * clues.length)];
        }

        function renderShadowGame() {
            const content = document.getElementById('shadow-content');
            const poem = shadowGame.currentPoem;
            const lines = poem.content.split(/[，。]/);

            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-6 mb-6">
                    <h3 class="text-2xl font-bold text-center mb-4">猜猜这是哪首古诗？</h3>

                    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h4 class="font-bold text-blue-800 mb-2">🔍 线索：</h4>
                        <p class="text-blue-700">${shadowGame.currentClue}</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        ${lines.filter(line => line.trim()).map((line, index) => `
                            <div class="p-4 bg-white border-2 border-dashed border-gray-300 rounded-lg text-center">
                                ${shadowGame.revealedLines.includes(index) ?
                                    `<span class="text-lg font-kai text-green-800">${line.trim()}</span>` :
                                    `<span class="text-gray-400">点击显示第${index + 1}句</span>`
                                }
                                ${!shadowGame.revealedLines.includes(index) ?
                                    `<button onclick="revealShadowLine(${index})" class="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">显示</button>` :
                                    ''
                                }
                            </div>
                        `).join('')}
                    </div>

                    ${shadowGame.isComplete ? `
                        <div class="text-center p-4 bg-green-100 border border-green-300 rounded-lg">
                            <h4 class="text-xl font-bold text-green-800 mb-2">🎉 恭喜你！</h4>
                            <p class="text-green-700 mb-2">你猜对了！这首诗是《${poem.title}》</p>
                            <p class="text-sm text-gray-600 mb-4">作者：${poem.author} · ${poem.grade}年级</p>
                            <button onclick="startShadowGame()" class="px-6 py-3 bg-green-500 text-white rounded-lg font-bold hover:bg-green-600">
                                再来一首
                            </button>
                        </div>
                    ` : `
                        <div class="text-center">
                            <div class="mb-4">
                                <p class="text-gray-600 mb-2">已显示 ${shadowGame.revealedLines.length} / ${lines.filter(line => line.trim()).length} 句</p>
                            </div>
                            <div class="flex justify-center gap-4">
                                <button onclick="revealAllShadowLines()" class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600">
                                    显示全部
                                </button>
                                <button onclick="startShadowGame()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                                    换一首
                                </button>
                            </div>
                        </div>
                    `}
                </div>
            `;
        }

        function revealShadowLine(lineIndex) {
            if (!shadowGame.revealedLines.includes(lineIndex)) {
                shadowGame.revealedLines.push(lineIndex);

                const poem = shadowGame.currentPoem;
                const lines = poem.content.split(/[，。]/).filter(line => line.trim());

                if (shadowGame.revealedLines.length >= lines.length) {
                    shadowGame.isComplete = true;
                }

                renderShadowGame();
            }
        }

        function revealAllShadowLines() {
            const poem = shadowGame.currentPoem;
            const lines = poem.content.split(/[，。]/).filter(line => line.trim());
            shadowGame.revealedLines = Array.from({length: lines.length}, (_, i) => i);
            shadowGame.isComplete = true;
            renderShadowGame();
        }

        // ========== 连句成诗游戏逻辑 ==========
        let connectGame = {
            currentPoem: null,
            correctOrder: [],
            shuffledLines: [],
            selectedLines: [],
            isComplete: false
        };

        function startConnectGame() {
            if (selectedPoems.length === 0) {
                alert('请先选择古诗！');
                return;
            }

            // 随机选择一首诗
            connectGame.currentPoem = selectedPoems[Math.floor(Math.random() * selectedPoems.length)];
            connectGame.selectedLines = [];
            connectGame.isComplete = false;

            // 分割诗句并打乱顺序
            setupConnectGame();

            // 渲染游戏界面
            renderConnectGame();
        }

        function setupConnectGame() {
            const poem = connectGame.currentPoem;
            // 按标点符号分割诗句
            connectGame.correctOrder = poem.content.split(/[，。]/).filter(line => line.trim());

            // 打乱顺序
            connectGame.shuffledLines = [...connectGame.correctOrder].sort(() => Math.random() - 0.5);
        }

        function renderConnectGame() {
            const content = document.getElementById('connect-content');
            const poem = connectGame.currentPoem;

            content.innerHTML = `
                <div class="bg-poemBg rounded-lg p-6 mb-6">
                    <h3 class="text-2xl font-bold text-center mb-2">${poem.title}</h3>
                    <p class="text-center text-gray-600 mb-6">${poem.author} · ${poem.grade}年级</p>

                    <div class="mb-6">
                        <h4 class="text-lg font-bold mb-3 text-center">请按正确顺序点击诗句：</h4>
                        <div class="min-h-[120px] p-4 bg-white border-2 border-dashed border-gray-300 rounded-lg mb-4">
                            ${connectGame.selectedLines.length === 0 ?
                                '<p class="text-gray-400 text-center">点击下方的诗句来排序</p>' :
                                connectGame.selectedLines.map((line, index) =>
                                    `<div class="inline-block m-1 px-3 py-2 bg-blue-100 border border-blue-300 rounded-lg">
                                        <span class="text-blue-800">${index + 1}. ${line}</span>
                                        <button onclick="removeConnectLine(${index})" class="ml-2 text-red-500 hover:text-red-700">×</button>
                                    </div>`
                                ).join('')
                            }
                        </div>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-lg font-bold mb-3 text-center">可选择的诗句：</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            ${connectGame.shuffledLines.map((line, index) =>
                                connectGame.selectedLines.includes(line) ?
                                    `<div class="p-3 bg-gray-100 border border-gray-300 rounded-lg text-center text-gray-400">
                                        ${line} (已选择)
                                    </div>` :
                                    `<button onclick="selectConnectLine('${line}')" class="p-3 bg-white border-2 border-blue-300 rounded-lg text-center hover:bg-blue-50 transition-colors">
                                        ${line}
                                    </button>`
                            ).join('')}
                        </div>
                    </div>

                    ${connectGame.isComplete ? `
                        <div class="text-center p-4 bg-green-100 border border-green-300 rounded-lg">
                            <h4 class="text-xl font-bold text-green-800 mb-2">🎉 恭喜你！</h4>
                            <p class="text-green-700 mb-4">你成功完成了诗句排序！</p>
                            <div class="mb-4 p-3 bg-white rounded-lg">
                                <p class="font-bold mb-2">完整的诗：</p>
                                <p class="text-lg font-kai text-gray-800">${connectGame.correctOrder.join('，')}。</p>
                            </div>
                            <button onclick="startConnectGame()" class="px-6 py-3 bg-green-500 text-white rounded-lg font-bold hover:bg-green-600">
                                再来一首
                            </button>
                        </div>
                    ` : `
                        <div class="text-center">
                            <p class="text-gray-600 mb-4">已选择 ${connectGame.selectedLines.length} / ${connectGame.correctOrder.length} 句</p>
                            <div class="flex justify-center gap-4">
                                <button onclick="checkConnectOrder()" class="px-6 py-3 bg-blue-500 text-white rounded-lg font-bold hover:bg-blue-600" ${connectGame.selectedLines.length !== connectGame.correctOrder.length ? 'disabled' : ''}>
                                    检查答案
                                </button>
                                <button onclick="resetConnectGame()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                                    重新排序
                                </button>
                                <button onclick="startConnectGame()" class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600">
                                    换一首
                                </button>
                            </div>
                        </div>
                    `}
                </div>
            `;
        }

        function selectConnectLine(line) {
            if (!connectGame.selectedLines.includes(line)) {
                connectGame.selectedLines.push(line);
                renderConnectGame();
            }
        }

        function removeConnectLine(index) {
            connectGame.selectedLines.splice(index, 1);
            renderConnectGame();
        }

        function resetConnectGame() {
            connectGame.selectedLines = [];
            renderConnectGame();
        }

        function checkConnectOrder() {
            const isCorrect = connectGame.selectedLines.length === connectGame.correctOrder.length &&
                connectGame.selectedLines.every((line, index) => line === connectGame.correctOrder[index]);

            if (isCorrect) {
                connectGame.isComplete = true;
                renderConnectGame();
            } else {
                alert('顺序不对哦，再试试看！提示：古诗通常有固定的韵律和逻辑顺序。');
            }
        }
    </script>
</body>
</html>
